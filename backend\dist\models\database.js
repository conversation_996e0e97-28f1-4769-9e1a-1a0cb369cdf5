"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabase = getDatabase;
exports.initDatabase = initDatabase;
exports.closeDatabase = closeDatabase;
const sqlite3_1 = __importDefault(require("sqlite3"));
const path_1 = __importDefault(require("path"));
// 数据库实例
let db;
// 获取数据库实例
function getDatabase() {
    if (!db) {
        throw new Error('数据库未初始化');
    }
    return db;
}
// 初始化数据库
async function initDatabase() {
    return new Promise((resolve, reject) => {
        const dbPath = path_1.default.join(__dirname, '../../data/erp.db');
        // 确保数据目录存在
        const fs = require('fs');
        const dataDir = path_1.default.dirname(dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
        db = new sqlite3_1.default.Database(dbPath, (err) => {
            if (err) {
                reject(err);
                return;
            }
            console.log('连接到SQLite数据库');
            // 设置UTF-8编码
            db.run("PRAGMA encoding = 'UTF-8'", (err) => {
                if (err) {
                    console.error('设置编码失败:', err);
                }
            });
            createTables()
                .then(() => resolve())
                .catch(reject);
        });
    });
}
// 创建数据表
async function createTables() {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // 用户表
            db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          role VARCHAR(20) DEFAULT 'user',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 原材料表
            db.run(`
        CREATE TABLE IF NOT EXISTS materials (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
            // 成品表
            db.run(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          sale_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, async (err) => {
                if (err) {
                    reject(err);
                }
                else {
                    console.log('数据表创建成功');
                    try {
                        await createDefaultUser();
                        resolve();
                    }
                    catch (error) {
                        reject(error);
                    }
                }
            });
        });
    });
}
// 创建默认用户
async function createDefaultUser() {
    return new Promise((resolve, reject) => {
        // 检查是否已存在admin用户
        db.get('SELECT * FROM users WHERE username = ?', ['admin'], async (err, row) => {
            if (err) {
                reject(err);
                return;
            }
            if (!row) {
                // 如果不存在admin用户，则创建
                const bcrypt = require('bcryptjs');
                const hashedPassword = await bcrypt.hash('123456', 10);
                db.run('INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)', ['admin', hashedPassword, '<EMAIL>', 'admin'], (err) => {
                    if (err) {
                        reject(err);
                    }
                    else {
                        console.log('默认admin用户创建成功');
                        resolve();
                    }
                });
            }
            else {
                console.log('admin用户已存在');
                resolve();
            }
        });
    });
}
// 关闭数据库连接
function closeDatabase() {
    if (db) {
        db.close((err) => {
            if (err) {
                console.error('关闭数据库时出错:', err);
            }
            else {
                console.log('数据库连接已关闭');
            }
        });
    }
}
//# sourceMappingURL=database.js.map