export interface User {
    id: number;
    username: string;
    password: string;
    email?: string;
    role: 'admin' | 'user';
    created_at: string;
    updated_at: string;
}
export interface UserCreateInput {
    username: string;
    password: string;
    email?: string;
    role?: 'admin' | 'user';
}
export interface UserLoginInput {
    username: string;
    password: string;
}
export interface Material {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface MaterialCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface MaterialUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface Product {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    sale_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface ProductCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface ProductUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    search?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
//# sourceMappingURL=index.d.ts.map